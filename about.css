/* About Us Page Styles */

/* Import CSS Custom Properties from main styles */
/* These variables ensure consistent branding across all pages */

/* Enhanced Hero Banner */
.hero-banner {
    position: relative;
    width: 100%;
    min-height: 100vh;
    background-color: var(--bg-hero-fallback); /* Fallback color */
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    background-attachment: fixed; /* Parallax effect */
    display: flex;
    align-items: center;
    color: var(--text-on-dark);
    overflow: hidden;
}

/* Enhanced overlay for better text readability */
.hero-banner .hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(58, 44, 26, 0.7) 0%,
        rgba(0, 0, 0, 0.6) 50%,
        rgba(58, 44, 26, 0.8) 100%
    );
    backdrop-filter: blur(1px); /* Subtle blur for modern effect */
}

.hero-text-container {
    position: relative;
    z-index: 1;
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-text {
    text-align: left;
    max-width: 80%; /* Max width for text */
}

.hero-text h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 3rem;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 4px rgba(0,0,0,0.6);
}

.hero-text p {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.2rem;
    max-width: 550px;
}

/* About Page Content Sections */
.about-content-section {
    padding: 4rem 0;
    background-color: var(--bg-primary);
}

.about-content-section:nth-child(even) {
    background-color: var(--bg-secondary);
}

.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.text-column h2 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700; /* Bold Heading */
    font-size: 2.5rem; /* Larger, more prominent heading */
    color: var(--dark-gray);
    margin-bottom: 1.5rem;
}

.text-column p {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem; /* Standard body text size */
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.text-column p strong {
    color: var(--primary-brown); /* Highlight key terms */
}

.image-column img {
    width: 100%;
    height: auto;
    border-radius: 10px; /* Softer, modern look */
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1); /* Subtle glow/shadow */
}

/* RTL support */
.text-column[dir="rtl"] {
    text-align: right;
}

/* Team Showcase */
.team-showcase {
    padding: 4rem 0;
    background-color: #f9f9f9;
}

.team-showcase h2 {
    text-align: center;
    margin-bottom: 3rem;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 2rem;
    justify-items: center;
}

.team-member {
    position: relative;
    text-align: center;
}

.team-member img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 5px solid transparent;
    transition: border-color 0.3s, transform 0.3s;
}

.team-member:hover img {
    border-color: #8B4513;
    transform: scale(1.05);
}

.team-member .tooltip {
    visibility: hidden;
    width: 200px;
    background-color: #333;
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 10px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
}

.team-member:hover .tooltip {
    visibility: visible;
    opacity: 1;
}

.team-member h4 {
    margin-top: 1rem;
    font-family: 'Poppins', sans-serif;
}

.team-member p {
    font-family: 'Open Sans', sans-serif;
    color: #666;
}

/* Partners Section */
.partners-section {
    padding: 4rem 0;
    background-color: #FAF9F7;
    border-top: 1px solid #EEEEEE;
    border-bottom: 1px solid #EEEEEE;
}

.partners-section h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 4rem;
}

.logo-grid {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 3rem 2rem;
}

.logo-item {
    width: 150px;
    text-align: center;
}

.logo-item img {
    height: 60px;
    width: auto;
    max-width: 100%;
    filter: grayscale(1) opacity(0.6);
    transition: transform 0.3s ease-in-out, filter 0.3s ease-in-out;
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.logo-item p {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.logo-item:hover img,
.logo-item img:focus {
    filter: grayscale(0) opacity(1);
    transform: scale(1.1);
}

.logo-item img:focus {
    outline: 2px solid #8B4513;
    outline-offset: 4px;
}

/* Responsive Design */

/* Large tablets and small desktops */
@media (max-width: 1024px) {
    .about-content-section {
        padding: 3rem 0;
    }

    .text-column h2 {
        font-size: 2rem;
    }

    .timeline-container .title,
    .timeline .title {
        font-size: 2.5rem;
        margin-bottom: 2rem;
    }
}

/* Tablets */
@media (max-width: 768px) {
    .hero-text h1 {
        font-size: 2.2rem;
    }

    .hero-text p {
        font-size: 1.1rem;
    }

    .content-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .text-column {
        text-align: center;
    }
    
    .text-column[dir="rtl"] {
        text-align: center;
    }

    /* Mobile stacking order */
    .content-grid.text-first-on-mobile .text-column {
        order: 1;
    }
    .content-grid.text-first-on-mobile .image-column {
        order: 2;
    }

    .content-grid.image-first-on-mobile .image-column {
        order: 1;
    }
    .content-grid.image-first-on-mobile .text-column {
        order: 2;
    }
    
    .logo-grid {
        flex-direction: column;
        align-items: center;
        gap: 2rem;
    }

    .partners-section {
        padding: 3rem 0;
    }

    .partners-section h2,
    .team-showcase h2,
    .text-column h2 {
        font-size: 2rem;
    }

    /* Mobile Timeline Title Adjustments */
    .timeline .title {
        font-size: 32px; /* Reduced for mobile */
        margin-bottom: 2rem;
    }

    .timeline .swiper-slide .timeline-year {
        font-size: 36px; /* Adjusted for mobile */
        margin-bottom: 20px;
    }

    .timeline .swiper-slide .timeline-title {
        font-size: 28px; /* Adjusted for mobile */
        margin-bottom: 15px;
    }

    .timeline .swiper-slide .timeline-text {
        font-size: 14px; /* Slightly smaller for mobile */
        line-height: 1.5;
    }

    /* Mobile Navigation Adjustments */
    .timeline .swiper-button-next,
    .timeline .swiper-button-prev {
        width: 40px;
        height: 40px;
        top: 50%;
        transform: translateY(-50%);
    }

    .timeline .swiper-button-prev {
        left: 10px;
    }

    .timeline .swiper-button-next {
        right: 10px;
    }

    .timeline .swiper-button-next:hover,
    .timeline .swiper-button-prev:hover {
        transform: translateY(-50%) scale(1.05); /* Reduced scale for mobile */
    }

    /* Hide pagination on mobile for cleaner look */
    .timeline .swiper-pagination {
        display: none !important;
    }

    /* Mobile background image optimizations */
    .hero-banner {
        background-attachment: scroll; /* Disable parallax on mobile for better performance */
        min-height: 80vh; /* Reduce height on mobile */
    }

    .timeline .swiper-slide {
        background-attachment: scroll; /* Better mobile performance */
    }

    .timeline .swiper-slide:hover {
        transform: none; /* Disable hover effects on mobile */
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .hero-text h1 {
        font-size: 1.8rem;
        line-height: 1.2;
    }

    .hero-text p {
        font-size: 0.95rem;
    }

    .about-content-section {
        padding: 2rem 0;
    }

    .text-column h2 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .text-column p {
        font-size: 0.9rem;
        line-height: 1.6;
    }

    .timeline .title {
        font-size: 1.8rem;
        margin-bottom: 1.5rem;
    }

    .timeline .swiper-slide .timeline-year {
        font-size: 2rem;
        margin-bottom: 15px;
    }

    .timeline .swiper-slide .timeline-title {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }

    .timeline .swiper-slide .timeline-text {
        font-size: 0.8rem;
        line-height: 1.4;
    }

    .cta-button-brown {
        padding: 0.8rem 1.5rem;
        font-size: 1rem;
    }
}

html {
    scroll-behavior: smooth;
}

.cta-button-brown {
    background-color: #8B4513;
    color: #FFFFFF;
    padding: 1rem 2.5rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    border: 2px solid #8B4513;
    transition: background-color 0.3s ease, color 0.3s ease, transform 0.3s ease;
    margin-top: 2rem;
    display: inline-block;
}

.cta-button-brown:hover {
    background-color: #FFFFFF;
    color: #8B4513;
    transform: scale(1.05);
}

/* Swiper Timeline Styles */
.timeline-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  flex-direction: column;
  padding: 2rem 0;
}

/* Enhanced Timeline Title Styling */
.timeline-container .title {
  font-family: 'Poppins', sans-serif;
  font-size: 42px;
  color: #333333; /* Improved contrast from #616161 */
  font-style: normal; /* Removed italic for better readability */
  font-weight: 700; /* Reduced from 800 for better balance */
  margin-bottom: 3rem;
  text-align: center;
  line-height: 1.2;
  text-shadow: none; /* Remove any potential text shadows */
  position: relative;
}

.timeline .title {
  font-family: 'Poppins', sans-serif;
  font-size: 42px;
  color: #333333; /* Improved contrast from #616161 */
  font-style: normal; /* Removed italic for better readability */
  font-weight: 700; /* Reduced from 800 for better balance */
  margin-bottom: 3rem;
  text-align: center;
  line-height: 1.2;
  text-shadow: none; /* Remove any potential text shadows */
  position: relative;
}

/* Add subtle underline accent */
.timeline .title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: #8B4513;
  border-radius: 2px;
}

/* Enhanced Timeline Container */
.timeline {
  width: 100%;
  background-color: #fff;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15); /* Softer, more modern shadow */
  border-radius: 12px; /* Added border radius for modern look */
  overflow: hidden; /* Ensure content doesn't overflow rounded corners */
  margin: 2rem 0; /* Add vertical spacing */
}

.timeline .swiper-container {
  height: 600px;
  width: 100%;
  position: relative;
  border-radius: 12px; /* Match parent border radius */
}

.timeline .swiper-wrapper {
  transition: 2s cubic-bezier(.68, -0.4, .27, 1.34) .2s;
}

/* Enhanced Timeline Slide Background */
.timeline .swiper-slide {
  position: relative;
  color: #fff;
  overflow: hidden;
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  background-attachment: local; /* Ensures proper mobile performance */
  transition: transform 0.3s ease; /* Smooth transitions */
}

/* Add subtle hover effect for interactive feel */
.timeline .swiper-slide:hover {
  transform: scale(1.02);
}

/* Ensure images are optimized for different screen densities */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .timeline .swiper-slide {
    background-size: cover;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Enhanced slide overlay for better readability */
.timeline .swiper-slide::after {
  content: "";
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(0, 0, 0, 0.4) 0%,
    rgba(0, 0, 0, 0.6) 50%,
    rgba(0, 0, 0, 0.8) 100%
  ); /* Improved gradient overlay for better text contrast */
  border-radius: 0; /* Remove border radius for full coverage */
}

.timeline .swiper-slide-content {
  position: absolute;
  text-align: center;
  width: 80%;
  max-width: 310px;
  right: 50%;
  top: 13%;
  transform: translate(50%, 0);
  font-size: 12px;
  z-index: 2;
}

/* Enhanced Timeline Year Styling */
.timeline .swiper-slide .timeline-year {
  display: block;
  font-family: 'Poppins', sans-serif;
  font-style: normal; /* Removed italic for better readability */
  font-size: 48px; /* Slightly larger for better prominence */
  font-weight: 600; /* Increased from 300 for better visibility */
  margin-bottom: 30px; /* Reduced from 50px for better spacing */
  transform: translate3d(20px, 0, 0);
  color: var(--accent-gold);
  opacity: 0;
  transition: .2s ease .4s;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* Added for better contrast */
}

/* Enhanced Timeline Title Styling */
.timeline .swiper-slide .timeline-title {
  font-family: 'Poppins', sans-serif;
  font-weight: 700; /* Reduced from 800 for better balance */
  font-size: 36px; /* Slightly larger for better readability */
  margin: 0 0 25px; /* Reduced bottom margin */
  opacity: 0;
  transform: translate3d(20px, 0, 0);
  transition: .2s ease .5s;
  color: #FFFFFF;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5); /* Enhanced text shadow for better contrast */
  line-height: 1.2;
}

/* Enhanced Timeline Text Styling */
.timeline .swiper-slide .timeline-text {
  font-family: 'Open Sans', sans-serif;
  font-size: 16px;
  line-height: 1.6; /* Improved from 1.5 for better readability */
  opacity: 0;
  transform: translate3d(20px, 0, 0);
  transition: .2s ease .6s;
  color: #FFFFFF;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6); /* Enhanced text shadow for better contrast */
  font-weight: 400;
}

.timeline .swiper-slide-active .timeline-year {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: .4s ease 1.6s;
}

.timeline .swiper-slide-active .timeline-title {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: .4s ease 1.7s;
}

.timeline .swiper-slide-active .timeline-text {
  opacity: 1;
  transform: translate3d(0, 0, 0);
  transition: .4s ease 1.8s;
}

/* Enhanced Timeline Pagination */
.timeline .swiper-pagination {
  right: 15% !important;
  height: 100%;
  display: none;
  flex-direction: column;
  justify-content: center;
  font-family: 'Poppins', sans-serif;
  font-style: normal; /* Removed italic for better readability */
  font-weight: 600; /* Increased from 300 for better visibility */
  font-size: 16px; /* Reduced from 18px for better balance */
  z-index: 3;
}

.timeline .swiper-pagination::before {
  content: "";
  position: absolute;
  left: -35px;
  top: 10%;
  height: 80%;
  width: 2px; /* Increased from 1px for better visibility */
  background: linear-gradient(
    to bottom,
    transparent 0%,
    rgba(212, 160, 36, 0.3) 20%,
    rgba(212, 160, 36, 0.8) 50%,
    rgba(212, 160, 36, 0.3) 80%,
    transparent 100%
  );
  border-radius: 1px;
}

.timeline .swiper-pagination-bullet {
  width: auto;
  height: auto;
  text-align: center;
  opacity: 1;
  background: transparent;
  color: rgba(212, 160, 36, 0.7);
  margin: 20px 0 !important; /* Increased spacing */
  position: relative;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.timeline .swiper-pagination-bullet:hover {
  color: var(--accent-gold);
  background-color: var(--accent-gold-light);
}

.timeline .swiper-pagination-bullet::before {
  content: "";
  position: absolute;
  top: 50%;
  left: -40px;
  width: 10px; /* Increased from 6px */
  height: 10px; /* Increased from 6px */
  border-radius: 50%;
  background-color: var(--accent-gold-medium);
  transform: translateY(-50%) scale(0.6);
  transition: all 0.3s ease;
}

.timeline .swiper-pagination-bullet-active {
  color: var(--accent-gold);
  background-color: var(--accent-gold-light);
  font-weight: 700;
}

.timeline .swiper-pagination-bullet-active::before {
  transform: translateY(-50%) scale(1);
  background-color: var(--accent-gold);
  box-shadow: 0 0 10px var(--shadow-accent);
}

/* Enhanced Navigation Buttons */
.timeline .swiper-button-next,
.timeline .swiper-button-prev {
  width: 50px;
  height: 50px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  top: 50%;
  transform: translateY(-50%);
  margin-top: 0;
  z-index: 3;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(212, 160, 36, 0.3);
}

.timeline .swiper-button-next:hover,
.timeline .swiper-button-prev:hover {
  background-color: var(--accent-gold);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 6px 20px var(--shadow-accent);
}

.timeline .swiper-button-prev {
  left: 20px;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23d4a024'%2F%3E%3C%2Fsvg%3E");
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

.timeline .swiper-button-prev:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M0%2C22L22%2C0l2.1%2C2.1L4.2%2C22l19.9%2C19.9L22%2C44L0%2C22L0%2C22L0%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E");
}

.timeline .swiper-button-next {
  right: 20px;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23d4a024'%2F%3E%3C%2Fsvg%3E");
  background-size: 16px 16px;
  background-repeat: no-repeat;
  background-position: center;
}

.timeline .swiper-button-next:hover {
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D'http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg'%20viewBox%3D'0%200%2027%2044'%3E%3Cpath%20d%3D'M27%2C22L27%2C22L5%2C44l-2.1-2.1L22.8%2C22L2.9%2C2.1L5%2C0L27%2C22L27%2C22z'%20fill%3D'%23ffffff'%2F%3E%3C%2Fsvg%3E");
}

@media screen and (min-width: 768px) {
  /* Keep the enhanced gradient overlay for desktop */
  .timeline .swiper-slide::after {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.3) 0%,
      rgba(0, 0, 0, 0.5) 50%,
      rgba(0, 0, 0, 0.7) 100%
    );
  }

  .timeline .swiper-slide-content {
    right: 30%;
    top: 50%;
    transform: translateY(-50%);
    width: 310px;
    font-size: 11px;
    text-align: right;
  }

  .timeline .swiper-slide .timeline-year {
    margin-bottom: 15px;
    font-size: 38px;
  }

  .timeline .swiper-slide .timeline-title {
    font-size: 42px;
    margin: 0 0 20px;
  }

  .timeline .swiper-pagination {
    display: flex;
  }

  /* Enhanced desktop navigation positioning */
  .timeline .swiper-button-prev {
    top: 20%;
    left: auto;
    right: 80px;
    transform: translateY(-50%);
  }

  .timeline .swiper-button-prev:hover {
    transform: translateY(-50%) scale(1.1);
  }

  .timeline .swiper-button-next {
    top: auto;
    bottom: 20%;
    right: 80px;
    transform: translateY(50%);
  }

  .timeline .swiper-button-next:hover {
    transform: translateY(50%) scale(1.1);
  }
}

@media screen and (min-width: 1024px) {
  /* Enhanced overlay remains consistent with our gradient approach */
  .timeline .swiper-slide::after {
    background: linear-gradient(
      135deg,
      rgba(0, 0, 0, 0.2) 0%,
      rgba(0, 0, 0, 0.4) 50%,
      rgba(0, 0, 0, 0.6) 100%
    );
  }

  .timeline .swiper-slide-content {
    right: 25%;
  }

  /* Re-enable hover effects on larger screens */
  .timeline .swiper-slide:hover {
    transform: scale(1.02);
  }

  /* Enhanced hero banner for large screens */
  .hero-banner {
    background-attachment: fixed; /* Re-enable parallax on desktop */
    min-height: 100vh;
  }
}
