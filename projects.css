/* Projects Page Specific Styles */

/* Hero Section */
.hero-minimal {
    background-color: #FAF9F7;
    padding: 6rem 0;
    text-align: center;
}

.hero-minimal h1 {
    font-family: 'Poppins', sans-serif;
    font-weight: 700;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.hero-minimal p {
    font-family: 'Open Sans', sans-serif;
    font-size: 1.2rem;
    color: #666;
}

/* Filter Bar */
.filter-bar {
    padding: 2rem 0;
    background-color: #FFFFFF;
}

.filter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 1.5rem;
}

.search-box {
    flex-grow: 1;
    position: relative;
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid #DDDDDD;
    border-radius: 2rem;
    font-family: 'Open Sans', sans-serif;
}

.search-box::before {
    content: '🔍';
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
}

.filter-dropdowns {
    display: flex;
    gap: 1rem;
}

.filter-dropdowns select {
    padding: 0.75rem 1rem;
    border: 1px solid #DDDDDD;
    border-radius: 0.5rem;
    background-color: white;
    font-family: 'Open Sans', sans-serif;
    cursor: pointer;
}

.filter-dropdowns select:hover {
    border-color: #8B4513;
}

/* Projects Grid */
.projects-grid {
    padding: 4rem 0;
    background-color: #FAF9F7;
}

.project-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
}

.project-card {
    background-color: #FFFFFF;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    padding: 1.5rem;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.project-card h3 {
    font-family: 'Poppins', sans-serif;
    font-weight: 600;
    font-size: 1.25rem;
    margin-bottom: 0.75rem;
}

.project-card .location,
.project-card .dates {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.project-card .description {
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    margin-bottom: 1rem;
    flex-grow: 1;
}

.project-card .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #E0E0E0;
    border-radius: 4px;
    margin-bottom: 1rem;
    overflow: hidden;
}

.project-card .progress {
    height: 100%;
    background-color: #8B4513;
}

.project-card .cta-btn {
    display: inline-block;
    background-color: #8B4513;
    color: #FFFFFF;
    padding: 0.75rem 1.5rem;
    border-radius: 0.25rem;
    text-align: center;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    text-decoration: none;
    transition: background-color 0.3s;
}

.project-card .cta-btn:hover {
    background-color: #A0522D;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .project-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    .filter-controls {
        flex-direction: column;
        align-items: stretch;
    }
}

@media (max-width: 768px) {
    .project-cards {
        grid-template-columns: 1fr;
    }
    .filter-dropdowns {
        flex-direction: column;
    }
}
