/* Contact & Volunteer Page Styles */

.contact-volunteer-page {
    padding-top: 30px; /* Added padding */
    background-color: #FAF9F7;
}

.page-header {
    text-align: center;
    margin-bottom: 2rem;
}

.page-header h1 {
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.form-toggle {
    display: flex;
    justify-content: center;
    margin-bottom: 2rem;
}

.toggle-btn {
    background-color: #f0f0f0;
    border: 1px solid #ddd;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-family: 'Open Sans', sans-serif;
    font-size: 1rem;
    transition: background-color 0.3s, color 0.3s;
}

.toggle-btn.active {
    background-color: #8B4513;
    color: white;
    border-color: #8B4513;
}

.toggle-btn:first-child {
    border-top-left-radius: 0.5rem;
    border-bottom-left-radius: 0.5rem;
}

.toggle-btn:last-child {
    border-top-right-radius: 0.5rem;
    border-bottom-right-radius: 0.5rem;
}

.forms-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    padding: 2rem;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.form {
    display: none;
}

.form.active {
    display: block;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 0.25rem;
    font-family: 'Open Sans', sans-serif;
}

/* Recaptcha Styling */
.g-recaptcha {
    margin-bottom: 1.5rem;
    min-width: 300px;
}

@media (max-width: 400px) {
    .g-recaptcha {
        transform: scale(0.8);
        transform-origin: 0 0;
    }
}


.submit-btn {
    display: block;
    width: 100%;
    padding: 1rem;
    background-color: #8B4513;
    color: white;
    border: none;
    border-radius: 0.25rem;
    font-size: 1.1rem;
    font-family: 'Poppins', sans-serif;
    cursor: pointer;
    transition: background-color 0.3s;
}

.submit-btn:hover {
    background-color: #A0522D;
}

.feedback-message {
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 0.25rem;
    text-align: center;
    display: none;
}

.feedback-message.success {
    background-color: #D4EDDA;
    color: #155724;
    display: block;
}

.feedback-message.error {
    background-color: #F8D7DA;
    color: #721C24;
    display: block;
}


/* Map Section */
.map-section {
    padding: 4rem 0;
}

.map-wrapper {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 2rem;
    align-items: center;
}

.map-info h2 {
    font-family: 'Poppins', sans-serif;
    margin-bottom: 1rem;
}

.map-embed iframe {
    border-radius: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .map-wrapper {
        grid-template-columns: 1fr;
    }
}
