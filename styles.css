/* CSS Custom Properties - SEVO Brand Color Palette */
:root {
    /* Primary Brand Colors */
    --primary-brown: #8B4513;
    --primary-brown-dark: #6b350f;
    --primary-brown-light: rgba(139, 69, 19, 0.1);
    --primary-brown-medium: rgba(139, 69, 19, 0.15);

    /* Secondary/Accent Colors */
    --accent-gold: #d4a024;
    --accent-gold-light: rgba(212, 160, 36, 0.1);
    --accent-gold-medium: rgba(212, 160, 36, 0.3);
    --accent-gold-dark: #b88a14;

    /* Neutral Colors */
    --white: #FFFFFF;
    --off-white: #FAF9F7;
    --light-gray: #f9f9f9;
    --medium-gray: #666666;
    --dark-gray: #333333;
    --black: #000000;

    /* Text Colors */
    --text-primary: #000000;
    --text-secondary: #555555;
    --text-muted: #666666;
    --text-light: #cccccc;
    --text-on-dark: #FFFFFF;
    --text-on-light: #f0f0f0;

    /* Background Colors */
    --bg-primary: #FFFFFF;
    --bg-secondary: #FAF9F7;
    --bg-dark: #000000;
    --bg-hero-fallback: #3A2C1A;

    /* Border Colors */
    --border-light: #EEEEEE;
    --border-medium: #ddd;
    --border-dark: #333333;

    /* Shadow Colors */
    --shadow-light: rgba(0, 0, 0, 0.05);
    --shadow-medium: rgba(0, 0, 0, 0.1);
    --shadow-dark: rgba(0, 0, 0, 0.15);
    --shadow-primary: rgba(139, 69, 19, 0.2);
    --shadow-accent: rgba(212, 160, 36, 0.4);

    /* Typography System */
    --font-primary: 'Open Sans', sans-serif;
    --font-heading: 'Poppins', sans-serif;

    /* Font Sizes - Mobile First Approach */
    --font-size-xs: 0.75rem;    /* 12px */
    --font-size-sm: 0.875rem;   /* 14px */
    --font-size-base: 1rem;     /* 16px */
    --font-size-lg: 1.125rem;   /* 18px */
    --font-size-xl: 1.25rem;    /* 20px */
    --font-size-2xl: 1.5rem;    /* 24px */
    --font-size-3xl: 1.875rem;  /* 30px */
    --font-size-4xl: 2.25rem;   /* 36px */
    --font-size-5xl: 3rem;      /* 48px */
    --font-size-6xl: 3.75rem;   /* 60px */

    /* Font Weights */
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --font-weight-extrabold: 800;

    /* Line Heights */
    --line-height-tight: 1.25;
    --line-height-snug: 1.375;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.625;
    --line-height-loose: 2;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    color: var(--text-primary);
    background-color: var(--bg-secondary);
    line-height: var(--line-height-relaxed);
    font-size: var(--font-size-base);
}

/* Typography Hierarchy */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: var(--font-weight-bold);
    line-height: var(--line-height-tight);
    margin-bottom: 1rem;
}

h1 {
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-extrabold);
}

h2 {
    font-size: var(--font-size-4xl);
}

h3 {
    font-size: var(--font-size-3xl);
}

h4 {
    font-size: var(--font-size-2xl);
}

h5 {
    font-size: var(--font-size-xl);
}

h6 {
    font-size: var(--font-size-lg);
}

.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Navigation Bar */
.navbar {
    background-color: var(--bg-primary);
    box-shadow: 0 2px 10px var(--shadow-medium);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    transition: box-shadow 0.3s ease;
}

.navbar:hover {
    box-shadow: 0 4px 20px var(--shadow-dark);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 0;
}

/* Enhanced Logo Styling */
.navbar .logo {
    display: flex;
    align-items: center;
    transition: transform 0.3s ease;
}

.navbar .logo:hover {
    transform: scale(1.05);
}

.navbar .logo img {
    height: 50px; /* Increased from 40px for better visibility */
    width: auto;
    transition: filter 0.3s ease;
}

.navbar .logo img:hover {
    filter: brightness(1.1);
}

/* Enhanced Navigation Menu */
.nav-menu ul {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 0; /* Reset gap, we'll use margin for better control */
}

.nav-menu ul li {
    margin-left: 35px; /* Increased from 30px for better spacing */
    position: relative;
}

.nav-menu ul li:first-child {
    margin-left: 0;
}

.nav-menu ul li a {
    text-decoration: none;
    color: var(--text-primary);
    font-family: var(--font-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    padding: 8px 12px; /* Added padding for better click area */
    border-radius: 4px;
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
}

/* Enhanced hover effects */
.nav-menu ul li a:hover {
    color: var(--primary-brown);
    background-color: var(--primary-brown-light);
    transform: translateY(-2px);
}

.nav-menu ul li a.active {
    color: var(--primary-brown);
    background-color: var(--primary-brown-medium);
}

/* Improved Volunteer Button */
.volunteer-btn {
    background-color: var(--primary-brown);
    color: var(--text-on-dark);
    border: none;
    padding: 12px 24px; /* Increased padding for better proportion */
    border-radius: 6px; /* Slightly more rounded */
    font-family: var(--font-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-base);
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px var(--shadow-primary);
    position: relative;
    overflow: hidden;
}

.volunteer-btn:hover {
    background-color: var(--primary-brown-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow-primary);
}

.volunteer-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px var(--shadow-primary);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 0 1rem;
    color: var(--text-on-dark);
    background-image: url('images/hero.jpg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed; /* Parallax effect */
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5); /* Dark overlay */
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
    max-width: 800px;
}

.hero-content h1 {
    font-family: var(--font-heading);
    font-weight: var(--font-weight-extrabold);
    font-size: var(--font-size-6xl); /* Larger headline */
    margin-bottom: 1rem;
    line-height: var(--line-height-tight);
}

.hero-content p {
    font-family: var(--font-primary);
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-normal); /* Lighter weight */
    color: var(--text-on-light); /* Off-white for subheadline */
    line-height: var(--line-height-relaxed);
    margin-bottom: 2.5rem;
    max-width: 650px;
    margin-left: auto;
    margin-right: auto;
}

.hero .primary-btn {
    background-color: var(--accent-gold); /* Warm gold/brown */
    color: var(--text-on-dark);
    padding: 1rem 2.5rem;
    border-radius: 8px; /* Slightly more rounded */
    text-decoration: none;
    font-family: var(--font-primary);
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-lg);
    border: none;
    transition: background-color 0.3s ease;
}

.hero .primary-btn:hover {
    background-color: var(--accent-gold-dark); /* Darker shade on hover */
}

.primary-btn, .secondary-btn {
    padding: 15px 30px;
    border-radius: 4px;
    font-family: 'Open Sans', sans-serif;
    font-weight: 600;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.primary-btn {
    background-color: var(--primary-brown);
    color: var(--text-on-dark);
    border: 2px solid var(--primary-brown);
}

.primary-btn:hover {
    background-color: var(--primary-brown-dark);
    border-color: var(--primary-brown-dark);
}

.secondary-btn {
    background-color: transparent;
    color: var(--primary-brown);
    border: 2px solid var(--primary-brown);
}

/* Impact Stats Section */
.impact {
    padding: 80px 0;
    background-color: #FFFFFF;
}

.impact h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #000000;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
}

.stat-card {
    background-color: #FAF9F7;
    border-radius: 8px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    font-size: 48px;
    margin-bottom: 20px;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #8B4513;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 18px;
    color: #555555;
    font-weight: 600;
}

.secondary-btn:hover {
    background-color: #8B4513;
    color: #FFFFFF;
}

.secondary-btn:hover {
    background-color: #8B4513;
    color: #FFFFFF;
}

/* News Section */
.news {
    padding: 80px 0;
    background-color: #FAF9F7;
}

.news h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #000000;
}

.news-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
}

.news-card {
    background-color: #FFFFFF;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.news-card:hover {
    transform: translateY(-10px);
}

.news-image {
    height: 200px;
    background-color: #e0e0e0;
    background-image: linear-gradient(135deg, #8B4513 25%, transparent 25%), 
                      linear-gradient(225deg, #8B4513 25%, transparent 25%),
                      linear-gradient(315deg, #8B4513 25%, transparent 25%),
                      linear-gradient(45deg, #8B4513 25%, transparent 25%);
    background-size: 20px 20px;
    background-position: 0 0, 10px 0, 10px -10px, 0px 10px;
}

.news-content {
    padding: 20px;
}

.news-date {
    color: #8B4513;
    font-weight: 600;
    font-size: 14px;
}

.news-content h3 {
    font-size: 22px;
    margin: 10px 0;
    color: #000000;
}

.news-content p {
    color: #666666;
    font-size: 16px;
}

/* Contact Section */
.contact {
    padding: 80px 0;
    background-color: #FFFFFF;
}

.contact h2 {
    text-align: center;
    font-size: 36px;
    margin-bottom: 50px;
    color: #000000;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 50px;
}

.contact-info h3 {
    font-size: 28px;
    margin-bottom: 20px;
    color: #000000;
}

.contact-info p {
    margin-bottom: 30px;
    color: #666666;
    font-size: 18px;
}

.contact-details .contact-item {
    margin-bottom: 25px;
}

.contact-item h4 {
    font-size: 20px;
    margin-bottom: 5px;
    color: #000000;
}

.contact-item p {
    margin: 0;
    color: #666666;
    font-size: 16px;
}

.contact-form .form-group {
    margin-bottom: 20px;
}

.contact-form input,
.contact-form textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: 'Open Sans', sans-serif;
    font-size: 16px;
}

.contact-form input:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: #8B4513;
}

/* Partners Section */
.partners-section {
    padding: 4rem 0;
    background-color: #FAF9F7;
    border-top: 1px solid #EEEEEE;
    border-bottom: 1px solid #EEEEEE;
}

.partners-section h2 {
    text-align: center;
    font-family: 'Poppins', sans-serif;
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 4rem;
}

.logo-grid {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    flex-wrap: wrap;
    gap: 3rem 2rem;
}

.logo-item {
    width: 150px;
    text-align: center;
}

.logo-item img {
    height: 60px;
    width: auto;
    max-width: 100%;
    filter: grayscale(1) opacity(0.6);
    transition: transform 0.3s ease-in-out, filter 0.3s ease-in-out;
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 1rem;
}

.logo-item p {
    font-family: 'Open Sans', sans-serif;
    font-size: 0.9rem;
    color: #666;
    line-height: 1.4;
}

.logo-item:hover img,
.logo-item img:focus {
    filter: grayscale(0) opacity(1);
    transform: scale(1.1);
}

.logo-item img:focus {
    outline: 2px solid #8B4513;
    outline-offset: 4px;
}

/* Footer */
.footer {
    background-color: #000000;
    color: #FFFFFF;
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo h2 {
    font-size: 32px;
    margin-bottom: 20px;
}

.footer-logo p {
    color: #cccccc;
    font-size: 16px;
    line-height: 1.6;
}

.footer-links h3,
.footer-social h3 {
    font-size: 22px;
    margin-bottom: 20px;
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-links ul li a:hover {
    color: #8B4513;
}

.social-icons {
    display: flex;
    gap: 15px;
    align-items: center;
}

.social-icons a {
    color: #FFFFFF;
    transition: opacity 0.3s ease;
}

.social-icons a:hover {
    opacity: 0.7;
}

.social-icons img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: 2px solid #FFFFFF;
    padding: 4px;
    background-color: #FFFFFF;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #333333;
    color: #cccccc;
}

/* Global CTA Section */
.global-cta {
    background-color: #f4f4f4;
    padding: 4rem 0;
    text-align: center;
}

.cta-button {
    background-color: #8B4513;
    color: #FFFFFF;
    height: 45px;
    line-height: 45px;
    padding: 0 2rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: transform 0.3s ease, background-color 0.3s ease;
    display: inline-block;
}

.cta-button:hover {
    transform: scale(1.05);
    background-color: #6b350f;
}


/* Responsive Design */

/* Large tablets and small desktops */
@media screen and (max-width: 1024px) {
    .container {
        max-width: 95%;
        padding: 0 20px;
    }

    .hero-content h1 {
        font-size: var(--font-size-5xl);
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }

    .news-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 30px;
    }
}

/* Tablets */
@media screen and (max-width: 768px) {
    .navbar .container {
        padding: 15px 0;
    }

    /* Adjust logo size for mobile */
    .navbar .logo img {
        height: 45px; /* Slightly smaller on mobile */
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 75px; /* Adjusted for larger logo */
        flex-direction: column;
        background-color: #FFFFFF;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 20px 0;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-menu ul {
        flex-direction: column;
        gap: 0;
    }

    .nav-menu ul li {
        margin: 15px 0;
        margin-left: 0; /* Reset left margin for mobile */
    }

    /* Enhanced mobile menu item styling */
    .nav-menu ul li a {
        padding: 12px 20px;
        display: block;
        width: 100%;
        border-radius: 0;
    }

    .nav-menu ul li a:hover {
        background-color: rgba(139, 69, 19, 0.1);
        transform: none; /* Remove translateY on mobile */
    }

    /* Mobile volunteer button adjustments */
    .volunteer-btn {
        padding: 10px 20px;
        margin-top: 10px;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(1) {
        transform: translateY(8px) rotate(45deg);
    }
    
    .hamburger.active span:nth-child(3) {
        transform: translateY(-8px) rotate(-45deg);
    }
    
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .hero-content p {
        font-size: 1.1rem;
    }

    .hero {
        background-attachment: scroll; /* Disable parallax on mobile for performance */
        background-position: center top; /* Adjust focus for vertical screens */
        height: 90vh;
    }
    
    .news h2,
    .contact h2 {
        font-size: var(--font-size-3xl);
    }

    /* Mobile typography adjustments */
    h1 {
        font-size: var(--font-size-4xl);
    }

    h2 {
        font-size: var(--font-size-3xl);
    }

    h3 {
        font-size: var(--font-size-2xl);
    }
    
    .news-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    /* Improved mobile spacing */
    .stats, .news, .contact {
        padding: 50px 0;
    }

    .container {
        padding: 0 15px;
    }

    /* Better mobile button sizing */
    .primary-btn, .secondary-btn {
        padding: 12px 20px;
        font-size: var(--font-size-base);
        width: 100%;
        max-width: 280px;
        margin: 0 auto;
        display: block;
    }
}

@media screen and (max-width: 480px) {
    .hero {
        padding: 130px 0 70px;
        height: auto;
    }
    
    .hero-content h1 {
        font-size: var(--font-size-3xl);
    }

    .hero-content p {
        font-size: var(--font-size-base);
    }

    .news h2,
    .contact h2 {
        font-size: var(--font-size-2xl);
    }

    /* Small mobile typography adjustments */
    h1 {
        font-size: var(--font-size-3xl);
    }

    h2 {
        font-size: var(--font-size-2xl);
    }

    h3 {
        font-size: var(--font-size-xl);
    }
    
    .contact-wrapper {
        gap: 20px;
    }

    /* Extra small mobile optimizations */
    .container {
        padding: 0 10px;
    }

    .hero {
        padding: 120px 0 60px;
    }

    .stats, .news, .contact {
        padding: 40px 0;
    }

    /* Improved small mobile buttons */
    .primary-btn, .secondary-btn {
        padding: 10px 16px;
        font-size: var(--font-size-sm);
    }

    .volunteer-btn {
        padding: 8px 16px;
        font-size: var(--font-size-sm);
    }
}
