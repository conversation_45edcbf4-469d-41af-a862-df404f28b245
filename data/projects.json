{"projects": [{"id": 1, "name": "Women's Literacy Program", "location": "<PERSON><PERSON><PERSON>", "start_date": "2023-01-15", "end_date": "Ongoing", "category": "Education", "status": "Active", "description": "Training 500+ women in basic literacy and numeracy skills."}, {"id": 2, "name": "Clean Water Well", "location": "<PERSON><PERSON>", "start_date": "2022-11-01", "end_date": "2023-03-31", "category": "Water", "status": "Completed", "description": "Constructed a new well providing clean drinking water to over 1,000 residents."}, {"id": 3, "name": "Youth Microfinance Initiative", "location": "<PERSON><PERSON><PERSON>", "start_date": "2024-02-01", "end_date": "Ongoing", "category": "Microfinance", "status": "Active", "description": "Providing small loans and business training to young entrepreneurs."}, {"id": 4, "name": "Agricultural Training", "location": "<PERSON>", "start_date": "2023-06-10", "end_date": "2023-12-20", "category": "Education", "status": "Completed", "description": "Trained local farmers in sustainable and drought-resistant farming techniques."}, {"id": 5, "name": "Community Health Clinic", "location": "<PERSON><PERSON>", "start_date": "2024-05-01", "end_date": "Ongoing", "category": "Health", "status": "Active", "description": "Operating a mobile health clinic to provide basic medical services."}, {"id": 6, "name": "School Infrastructure Project", "location": "<PERSON><PERSON><PERSON>", "start_date": "2024-08-15", "end_date": "Upcoming", "category": "Education", "status": "Upcoming", "description": "Renovating and expanding the primary school to accommodate more students."}]}