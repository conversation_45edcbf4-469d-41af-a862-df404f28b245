// Mobile Navigation Toggle
const hamburger = document.querySelector('.hamburger');
const navMenu = document.querySelector('.nav-menu');

hamburger.addEventListener('click', () => {
    hamburger.classList.toggle('active');
    navMenu.classList.toggle('active');
});

// Close mobile menu when clicking on a link
document.querySelectorAll('.nav-menu a').forEach(link => {
    link.addEventListener('click', () => {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    });
});

// Close mobile menu when clicking outside
document.addEventListener('click', (event) => {
    const isClickInsideNav = navMenu.contains(event.target) || hamburger.contains(event.target);
    
    if (!isClickInsideNav && navMenu.classList.contains('active')) {
        hamburger.classList.remove('active');
        navMenu.classList.remove('active');
    }
});

// Form submission handling
const contactForm = document.querySelector('.contact-form form');
if (contactForm) {
    contactForm.addEventListener('submit', (e) => {
        e.preventDefault();
        
        // Get form values
        const name = contactForm.querySelector('input[type="text"]').value;
        const email = contactForm.querySelector('input[type="email"]').value;
        const message = contactForm.querySelector('textarea').value;
        
        // In a real application, you would send this data to a server
        console.log('Form submitted:', { name, email, message });
        
        // Show success message (in a real app, this would come from the server)
        alert('Thank you for your message! We will get back to you soon.');
        
        // Reset form
        contactForm.reset();
    });
}

// Load stats data from JSON file and animate counters
document.addEventListener('DOMContentLoaded', () => {
    fetch('data/stats.json')
        .then(response => response.json())
        .then(data => {
            const stats = data.stats;
            
            // Update the stats values
            document.getElementById('volunteers-count').textContent = stats[0].value;
            document.getElementById('staff-count').textContent = stats[1].value;
            document.getElementById('families-count').textContent = stats[2].value;
            
            // Animate counters when scrolled into view
            const statElements = document.querySelectorAll('.stat-value');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        const targetValue = parseInt(element.textContent);
                        let count = 0;
                        const duration = 2000; // ms
                        const increment = targetValue / (duration / 16); // 16ms per frame
                        
                        const timer = setInterval(() => {
                            count += increment;
                            if (count >= targetValue) {
                                element.textContent = targetValue;
                                clearInterval(timer);
                            } else {
                                element.textContent = Math.ceil(count);
                            }
                        }, 16);
                        
                        // Stop observing this element
                        observer.unobserve(element);
                    }
                });
            }, { threshold: 0.5 });
            
            statElements.forEach(element => {
                observer.observe(element);
            });
        })
        .catch(error => console.error('Error loading stats:', error));
});

// Load News Data
document.addEventListener('DOMContentLoaded', () => {
    fetch('data/news.json')
        .then(response => response.json())
        .then(data => {
            const newsGrid = document.querySelector('.news-grid');
            if (newsGrid) {
                newsGrid.innerHTML = ''; // Clear static content
                data.news.forEach(article => {
                    const newsCard = document.createElement('div');
                    newsCard.classList.add('news-card');
                    
                    let imageHtml = '<div class="news-image-placeholder"></div>';
                    if (article.image) {
                        imageHtml = `<div class="news-image" style="background-image: url('${article.image}');"></div>`;
                    }

                    newsCard.innerHTML = `
                        ${imageHtml}
                        <div class="news-content">
                            <span class="news-date">${article.date}</span>
                            <h3>${article.title}</h3>
                            <p>${article.excerpt}</p>
                        </div>
                    `;
                    newsGrid.appendChild(newsCard);
                });
            }
        })
        .catch(error => console.error('Error loading news:', error));
});

// Load Team Data
document.addEventListener('DOMContentLoaded', () => {
    const teamGrid = document.getElementById('team-grid');
    if (teamGrid) {
        fetch('data/staff.json')
            .then(response => response.json())
            .then(data => {
                data.staff.forEach(member => {
                    const memberDiv = document.createElement('div');
                    memberDiv.classList.add('team-member');
                    memberDiv.innerHTML = `
                        <img src="${member.photo}" alt="${member.name}">
                        <div class="tooltip">
                            <h4>${member.name}</h4>
                            <p>${member.role}</p>
                            <p>${member.bio}</p>
                        </div>
                        <h4>${member.name}</h4>
                        <p>${member.role}</p>
                    `;
                    teamGrid.appendChild(memberDiv);
                });
            })
            .catch(error => console.error('Error loading team data:', error));
    }
});

// Load and Filter Projects
document.addEventListener('DOMContentLoaded', () => {
    const projectsContainer = document.getElementById('project-cards-container');
    if (!projectsContainer) {
        return; // Don't run this script if we're not on the projects page
    }

    const searchInput = document.getElementById('search-input');
    const statusFilter = document.getElementById('status-filter');
    const locationFilter = document.getElementById('location-filter');
    const categoryFilter = document.getElementById('category-filter');

    let allProjects = [];

    fetch('data/projects.json')
        .then(response => response.json())
        .then(data => {
            allProjects = data.projects;
            populateFilters(allProjects);
            renderProjects(allProjects);
        })
        .catch(error => console.error('Error loading projects:', error));

    function populateFilters(projects) {
        const locations = [...new Set(projects.map(p => p.location))];
        const categories = [...new Set(projects.map(p => p.category))];

        locations.forEach(location => {
            const option = document.createElement('option');
            option.value = location;
            option.textContent = location;
            locationFilter.appendChild(option);
        });

        categories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = category;
            categoryFilter.appendChild(option);
        });
    }

    function renderProjects(projects) {
        projectsContainer.innerHTML = '';
        if (projects.length === 0) {
            projectsContainer.innerHTML = '<p>No projects match the current filters.</p>';
            return;
        }

        projects.forEach(project => {
            const card = document.createElement('div');
            card.classList.add('project-card');

            const progressHtml = project.status === 'Active' ? `
                <div class="progress-bar">
                    <div class="progress" style="width: ${Math.random() * 80 + 10}%"></div>
                </div>
            ` : '';

            card.innerHTML = `
                <h3>${project.name}</h3>
                <p class="location">📍 ${project.location}</p>
                <p class="dates">${project.start_date} - ${project.end_date}</p>
                <p class="description">${project.description}</p>
                ${progressHtml}
                <a href="#contact" class="cta-btn">Get Involved</a>
            `;
            projectsContainer.appendChild(card);
        });
    }

    function filterProjects() {
        let filteredProjects = [...allProjects];

        const searchTerm = searchInput.value.toLowerCase();
        if (searchTerm) {
            filteredProjects = filteredProjects.filter(p =>
                p.name.toLowerCase().includes(searchTerm) ||
                p.location.toLowerCase().includes(searchTerm) ||
                p.description.toLowerCase().includes(searchTerm)
            );
        }

        const status = statusFilter.value;
        if (status !== 'all') {
            filteredProjects = filteredProjects.filter(p => p.status === status);
        }

        const location = locationFilter.value;
        if (location !== 'all') {
            filteredProjects = filteredProjects.filter(p => p.location === location);
        }

        const category = categoryFilter.value;
        if (category !== 'all') {
            filteredProjects = filteredProjects.filter(p => p.category === category);
        }

        renderProjects(filteredProjects);
    }

    searchInput.addEventListener('input', filterProjects);
    statusFilter.addEventListener('change', filterProjects);
    locationFilter.addEventListener('change', filterProjects);
    categoryFilter.addEventListener('change', filterProjects);
});
