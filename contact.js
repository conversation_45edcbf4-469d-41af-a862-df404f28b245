document.addEventListener('DOMContentLoaded', () => {
    const contactBtn = document.getElementById('contact-btn');
    const volunteerBtn = document.getElementById('volunteer-btn');
    const contactForm = document.getElementById('contact-form');
    const volunteerForm = document.getElementById('volunteer-form');
    const projectDropdown = document.getElementById('volunteer-project');
    const formFeedback = document.getElementById('form-feedback');
    const phoneInputs = document.querySelectorAll('input[type="tel"]');

    let projectsLoaded = false;

    // Form toggling
    contactBtn.addEventListener('click', () => {
        contactBtn.classList.add('active');
        volunteerBtn.classList.remove('active');
        contactForm.classList.add('active');
        volunteerForm.classList.remove('active');
    });

    volunteerBtn.addEventListener('click', () => {
        volunteerBtn.classList.add('active');
        contactBtn.classList.remove('active');
        volunteerForm.classList.add('active');
        contactForm.classList.remove('active');
        
        // Lazy load projects only once
        if (!projectsLoaded) {
            loadProjects();
        }
    });

    // Populate project dropdown
    function loadProjects() {
        fetch('data/projects.json')
            .then(response => response.json())
            .then(data => {
                data.projects.forEach(project => {
                    const option = document.createElement('option');
                    option.value = project.name;
                    option.textContent = project.name;
                    projectDropdown.appendChild(option);
                });
                projectsLoaded = true;
            })
            .catch(error => {
                console.error('Error fetching projects:', error);
                // Add fallback options
                const fallbackProjects = ['General Volunteering', 'Community Support', 'Fundraising'];
                fallbackProjects.forEach(projectName => {
                    const option = document.createElement('option');
                    option.value = projectName;
                    option.textContent = projectName;
                    projectDropdown.appendChild(option);
                });
                projectsLoaded = true; // Mark as loaded even if fallback is used
            });
    }

    // Phone number validation and formatting
    phoneInputs.forEach(input => {
        input.addEventListener('input', (e) => {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length > 3 && value.length <= 6) {
                value = `(${value.slice(0, 3)}) ${value.slice(3)}`;
            } else if (value.length > 6) {
                value = `(${value.slice(0, 3)}) ${value.slice(3, 6)}-${value.slice(6, 10)}`;
            }
            e.target.value = value;
        });
        // Add pattern for basic validation
        input.pattern = "^\\(\\d{3}\\) \\d{3}-\\d{4}$";
    });

    // Handle form submission feedback
    const allForms = [contactForm, volunteerForm];
    allForms.forEach(form => {
        if(form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault(); // Prevent actual submission for this example
                
                let isValid = form.checkValidity();

                if (isValid) {
                    formFeedback.textContent = 'Thank you for your submission!';
                    formFeedback.className = 'feedback-message success';
                    form.reset();
                    // In a real scenario, the form would submit to Google Forms here.
                    // The target="hidden_iframe" would handle the response.
                } else {
                    formFeedback.textContent = 'Please fill out all required fields correctly.';
                    formFeedback.className = 'feedback-message error';
                }
                
                setTimeout(() => {
                    formFeedback.style.display = 'none';
                }, 5000);
            });
        }
    });
});
